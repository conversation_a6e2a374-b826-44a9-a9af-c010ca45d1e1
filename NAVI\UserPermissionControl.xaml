<UserControl x:Class="NAVI.UserPermissionControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <UserControl.Resources>
        <!-- 开发中页面样式 -->
        <Style x:Key="DevelopmentPageStyle" TargetType="Border">
            <Setter Property="Background" Value="#FFF8F9FA"/>
            <Setter Property="BorderBrush" Value="#FFE9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="40"/>
        </Style>
        
        <Style x:Key="DevelopmentTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#FF495057"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <Style x:Key="DevelopmentMessageStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Foreground" Value="#FF6C757D"/>
            <Setter Property="TextAlignment" Value="Left"/>
            <Setter Property="LineHeight" Value="24"/>
        </Style>
        
        <Style x:Key="DevelopmentUnderlineStyle" TargetType="Border">
            <Setter Property="Height" Value="3"/>
            <Setter Property="Background" Value="#FF007BFF"/>
            <Setter Property="Margin" Value="0,0,0,30"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
            <Setter Property="Width" Value="200"/>
        </Style>
    </UserControl.Resources>

    <Grid Background="#FFFFFFFF">
        <!-- 主内容区域 -->
        <Border Style="{StaticResource DevelopmentPageStyle}"
                Margin="50"
                VerticalAlignment="Top"
                HorizontalAlignment="Left"
                MinWidth="400"
                MinHeight="200">
            <StackPanel>
                <!-- 页面标题 -->
                <TextBlock Text="userPermission" 
                          Style="{StaticResource DevelopmentTitleStyle}"/>
                
                <!-- 标题下划线 -->
                <Border Style="{StaticResource DevelopmentUnderlineStyle}"/>
                
                <!-- 开发中消息 -->
                <TextBlock Text="この機能は開発中です。" 
                          Style="{StaticResource DevelopmentMessageStyle}"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
